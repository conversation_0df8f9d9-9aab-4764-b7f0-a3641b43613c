from mcp.server.fastmcp import FastMCP, Context
import os
import logging
import requests
import tempfile
import platform
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("BlenderMediaTools")

# Create MCP server
mcp = FastMCP(
    name="BlenderMediaTools",
    instructions="Tools for processing .blend files and media in Blender with Cloud Object Storage integration",
)

# Environment variables
COS_API_BASE_URL = os.getenv("CODE_ENGINE_URL")
DEFAULT_BUCKET = os.getenv("DEFAULT_BUCKET", "volumetric")
INPUT_BUCKET = os.getenv("INPUT_BUCKET", "volumetric-inputs")
OUTPUT_BUCKET = os.getenv("OUTPUT_BUCKET", "volumetric-outputs")

# Platform-specific paths
IS_LINUX = platform.system() == "Linux"
IS_UBUNTU = IS_LINUX and "ubuntu" in platform.platform().lower()

# Set default paths based on platform
if IS_LINUX:
    # For Linux/Ubuntu servers, use /tmp or /var/tmp for better permissions
    DEFAULT_WORK_DIR = os.getenv("BLENDER_WORK_DIR", "/tmp/blender_media")
    DEFAULT_OUTPUT_DIR = os.getenv("BLENDER_OUTPUT_DIR", "/var/tmp/blender_output")
else:
    # For Windows/Mac, try Desktop first
    DEFAULT_WORK_DIR = os.path.join(os.path.expanduser("~"), "Desktop", "input")
    DEFAULT_OUTPUT_DIR = os.path.join(os.path.expanduser("~"), "Desktop")

# Ensure directories exist with proper permissions
def ensure_directory(path: str, fallback: str = None) -> str:
    """Ensure directory exists and is writable, with fallback option"""
    try:
        Path(path).mkdir(parents=True, exist_ok=True, mode=0o755)
        
        # Test write permissions
        test_file = os.path.join(path, ".write_test")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        
        logger.info(f"Using directory: {path}")
        return path
    except Exception as e:
        logger.warning(f"Cannot use {path}: {e}")
        if fallback:
            try:
                Path(fallback).mkdir(parents=True, exist_ok=True, mode=0o755)
                logger.info(f"Using fallback directory: {fallback}")
                return fallback
            except Exception as e2:
                logger.error(f"Cannot use fallback {fallback}: {e2}")
        
        # Last resort: use system temp
        temp_dir = tempfile.mkdtemp(prefix="blender_")
        logger.info(f"Using temporary directory: {temp_dir}")
        return temp_dir

# Initialize working directories
WORK_DIR = ensure_directory(DEFAULT_WORK_DIR, tempfile.gettempdir())
OUTPUT_DIR = ensure_directory(DEFAULT_OUTPUT_DIR, WORK_DIR)


class BlenderConnection:
    def __init__(self, host: str = "localhost", port: int = 9876):
        self.base_url = f"http://{host}:{port}"
        self.session = requests.Session()
        self.session.timeout = 30  # Default timeout reduced
        
        # Add connection pooling for better performance
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=10,
            max_retries=3
        )
        self.session.mount('http://', adapter)
    
    def send_command(self, command_type: str, params: Dict[str, Any] = None, timeout: int = None) -> Dict[str, Any]:
        """Send a command to Blender via HTTP POST"""
        command = {
            "type": command_type,
            "params": params or {}
        }
        
        # Use custom timeout if provided
        request_timeout = timeout or self.session.timeout
        
        try:
            logger.info(f"Sending command: {command_type} (timeout: {request_timeout}s)")
            response = self.session.post(
                self.base_url,
                json=command,
                timeout=request_timeout
            )
            
            logger.info(f"Received response status: {response.status_code}")
            
            if response.status_code != 200:
                error_msg = f"HTTP error {response.status_code}: {response.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
            return response.json()
        
        except requests.exceptions.Timeout:
            logger.warning(f"Request timed out after {request_timeout}s - operation may still be running in Blender")
            # Return a timeout response instead of raising exception
            return {
                "status": "timeout",
                "message": f"Request timed out after {request_timeout}s. Operation may still be running in Blender. Check output directory for results."
            }
            
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error: {str(e)}")
            raise Exception(f"Connection to Blender lost: {str(e)}")
            
        except Exception as e:
            logger.error(f"HTTP communication error: {str(e)}")
            raise Exception(f"Communication error with Blender: {str(e)}")
    
    def __del__(self):
        """Clean up session on deletion"""
        if hasattr(self, 'session'):
            self.session.close()


def download_from_cos(object_key: str, cos_api_url: str = COS_API_BASE_URL) -> str:
    """Download file from COS with improved error handling and platform compatibility"""
    try:
        # The cos_app.py endpoint is /buckets/objects/{object_key}
        # It only reads from volumetric-input bucket
        download_url = f"{cos_api_url}/buckets/objects/{object_key}"
        logger.info(f"Download URL: {download_url}")
        
        response = requests.get(download_url, stream=True, timeout=30)
        response.raise_for_status()
        
        filename = os.path.basename(object_key)
        
        # Use platform-appropriate work directory
        download_path = os.path.join(WORK_DIR, filename)
        logger.info(f"Download path: {download_path}")

        # Download file with progress tracking
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(download_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0 and downloaded % (100 * 1024) == 0:  # Log every 100KB
                        progress = (downloaded / total_size) * 100
                        logger.debug(f"Download progress: {progress:.0f}%")
        
        if os.path.exists(download_path):
            file_size = os.path.getsize(download_path)
            logger.info(f"Downloaded {object_key} to: {download_path} (size: {file_size} bytes)")
            
            # Set proper permissions on Linux
            if IS_LINUX:
                os.chmod(download_path, 0o644)
        else:
            raise Exception(f"File not found after download: {download_path}")
        
        return download_path
        
    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to download {object_key} from COS: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"Unexpected error downloading {object_key}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def extract_blender_output(response: Dict[str, Any]) -> str:
    """Extract string output from Blender response with improved formatting"""
    if response.get("status") == "timeout":
        message = response.get("message", "Operation timed out")
        return f"⏱️ {message}"
    elif response.get("status") == "success":
        result = response.get("result", {})
        if isinstance(result, dict):
            executed_output = result.get("result", "")
            if executed_output:
                # Check for success/error markers in output
                output_str = str(executed_output)
                if "SUCCESS:" in output_str:
                    return f"✅ {output_str}"
                elif "ERROR:" in output_str:
                    return f"❌ {output_str}"
                else:
                    return output_str
            else:
                return "✅ Operation completed successfully (no output received)"
        else:
            return str(result)
    else:
        error = response.get("error", "Unknown error")
        message = response.get("message", "")
        if message:
            return f"❌ Operation failed: {error}\nDetails: {message}"
        else:
            return f"❌ Operation failed: {error}"


def cleanup_temp_files(file_path: str = None):
    """Clean up temporary files, handling platform-specific issues"""
    if not file_path:
        return
    
    try:
        if os.path.exists(file_path):
            # On Linux, ensure we have permissions
            if IS_LINUX:
                try:
                    os.chmod(file_path, 0o666)
                except:
                    pass
            
            os.remove(file_path)
            logger.debug(f"Cleaned up temporary file: {file_path}")
    except Exception as e:
        logger.warning(f"Failed to clean up temporary file {file_path}: {e}")


@mcp.tool()
def test_blender_connection(ctx: Context, blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """Test connection to Blender by executing a simple script and creating a test cube"""
    try:
        blender = BlenderConnection(blender_host, blender_port)
        code = """
import bpy
import sys
import platform

print("=== Blender Connection Test ===")
print(f"Blender version: {bpy.app.version_string}")
print(f"Python version: {sys.version}")
print(f"Platform: {platform.platform()}")
print(f"Current scene: {bpy.context.scene.name}")
print(f"Objects in scene: {len(bpy.context.scene.objects)}")

# Test creating a simple object
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 2))
print("Added a test cube at (0, 0, 2)")

# Check available paths
import os
print(f"Temp directory: {bpy.app.tempdir}")
print(f"User scripts: {bpy.utils.user_resource('SCRIPTS')}")
print("=== Test Complete ===")
"""
        response = blender.send_command("execute_code", {"code": code})
        return extract_blender_output(response)
            
    except Exception as e:
        return f"❌ Connection failed: {str(e)}"



@mcp.tool()
def list_cos_bucket_objects(ctx: Context) -> str:
    """List objects in Cloud Object Storage (lists from volumetric-input bucket)"""
    try:
        if not COS_API_BASE_URL:
            return "❌ COS_API_BASE_URL not configured. Please set the CODE_ENGINE_URL environment variable."
        
        # Note: cos_app.py lists from BUCKET_NAME_1 (volumetric-input)
        url = f"{COS_API_BASE_URL}/buckets/objects"
        logger.info(f"Listing objects from: {url}")
        
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        objects = response.json()
        if objects:
            result = f"📦 Found {len(objects)} objects in bucket 'volumetric-input':\n"
            for obj in objects:
                result += f"  - {obj}\n"
            return result
        else:
            return f"📦 Bucket 'volumetric-input' is empty"
            
    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to list objects: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
    except Exception as e:
        error_msg = f"Unexpected error listing bucket: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
    
@mcp.tool()
def load_blend_file_from_cos(ctx: Context, object_key: str,
                             blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """Load a .blend file from Cloud Object Storage into Blender"""
    temp_path = None
    try:
        # Validate file extension
        if not object_key.lower().endswith('.blend'):
            return f"❌ Error: File must be a .blend file. Provided: {object_key}"
        
        logger.info(f"Loading .blend file '{object_key}' from COS to Blender")
        
        if not COS_API_BASE_URL:
            return "❌ COS_API_BASE_URL not configured. Please set the CODE_ENGINE_URL environment variable."
        
        # Download from COS
        temp_path = download_from_cos(object_key, COS_API_BASE_URL)
        
        # Convert path for cross-platform compatibility
        clean_path = temp_path.replace("\\", "/")
        
        blender_code = f"""
import bpy
import os

try:
    blend_path = r"{clean_path}"
    object_key = "{object_key}"
    
    # Check if file exists
    if not os.path.exists(blend_path):
        raise Exception(f"Blend file not found at: {{blend_path}}")
    
    # Open the blend file
    print(f"Opening .blend file: {{object_key}}")
    bpy.ops.wm.open_mainfile(filepath=blend_path)
    
    # Get scene information
    scene = bpy.context.scene
    print(f"SUCCESS: Loaded .blend file '{{object_key}}'")
    print(f"Scene name: {{scene.name}}")
    print(f"Frame range: {{scene.frame_start}} - {{scene.frame_end}}")
    print(f"Resolution: {{scene.render.resolution_x}}x{{scene.render.resolution_y}}")
    print(f"FPS: {{scene.render.fps}}")
    
    # List objects in scene
    objects = bpy.context.scene.objects
    print(f"Objects in scene: {{len(objects)}}")
    for obj in objects[:10]:  # List first 10 objects
        print(f"  - {{obj.name}} ({{obj.type}})")
    if len(objects) > 10:
        print(f"  ... and {{len(objects) - 10}} more objects")
    
    # Check for video sequences or movie clips
    if scene.sequence_editor and scene.sequence_editor.sequences:
        print(f"Video sequences found: {{len(scene.sequence_editor.sequences)}}")
        for seq in scene.sequence_editor.sequences[:5]:
            print(f"  - {{seq.name}} ({{seq.type}})")
    
    # Check for movie clips
    if bpy.data.movieclips:
        print(f"Movie clips in file: {{len(bpy.data.movieclips)}}")
        for clip in bpy.data.movieclips:
            print(f"  - {{clip.name}} ({{clip.size[0]}}x{{clip.size[1]}}, {{clip.frame_duration}} frames)")
    
except Exception as e:
    error_msg = f"ERROR loading .blend file: {{str(e)}}"
    print(error_msg)
    raise Exception(error_msg)
"""
        
        blender = BlenderConnection(blender_host, blender_port)
        response = blender.send_command("execute_code", {"code": blender_code})
        result = extract_blender_output(response)
        
        return result
            
    except Exception as e:
        return f"❌ Error loading .blend file: {str(e)}"
    finally:
        # Clean up temporary file
        if temp_path and temp_path.startswith(tempfile.gettempdir()):
            cleanup_temp_files(temp_path)


@mcp.tool()
def change_video_resolution(ctx: Context, blend_file: str,
                            resolution_percentage: int = 100,
                            new_width: Optional[int] = None, 
                            new_height: Optional[int] = None, 
                            output_filename: Optional[str] = None,
                            max_frames: Optional[int] = None,
                            quality: str = "medium",
                            maintain_aspect_ratio: bool = True,
                            blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """
    Change the resolution of video output from a .blend file.
    Saves both the rendered video/image and a new .blend file with updated settings.
    
    Args:
        blend_file: Path to .blend file or name if already loaded
        resolution_percentage: Percentage to scale (e.g., 50 for half size, 200 for double)
        new_width: Optional specific width in pixels (overrides percentage if both provided)
        new_height: Optional specific height in pixels (overrides percentage if both provided)
        output_filename: Optional output filename (without extension)
        max_frames: Maximum frames to render (None for all, useful for previews)
        quality: Output quality - "low", "medium", "high"
        maintain_aspect_ratio: If True, maintains aspect ratio (applies when using specific dimensions)
        blender_host: Blender server host
        blender_port: Blender server port
    
    Returns:
        Status message with output file location
    """
    
    # Validate file extension
    if not blend_file.lower().endswith('.blend'):
        return f"❌ Error: Input must be a .blend file. Provided: {blend_file}"
    
    # Determine if using percentage or specific dimensions
    use_percentage = (new_width is None or new_height is None)
    
    if use_percentage:
        logger.info(f"Changing video resolution from {blend_file} to {resolution_percentage}%")
    else:
        logger.info(f"Changing video resolution from {blend_file} to {new_width}x{new_height}")
    
    if not output_filename:
        clean_name = os.path.basename(blend_file).replace(".blend", "").replace(" ", "_")
        if use_percentage:
            output_filename = f"{clean_name}_scaled_{resolution_percentage}pct"
        else:
            output_filename = f"{clean_name}_resized_{new_width}x{new_height}"
    
    # Use platform-appropriate output directory
    output_base = OUTPUT_DIR.replace("\\", "/")
    
    # Set quality parameters
    quality_settings = {
        "low": {"bitrate": 2000, "preset": "FAST", "crf": "LOW"},
        "medium": {"bitrate": 6000, "preset": "GOOD", "crf": "MEDIUM"},
        "high": {"bitrate": 12000, "preset": "BEST", "crf": "HIGH"}
    }
    settings = quality_settings.get(quality.lower(), quality_settings["medium"])
    
    blender_code = f"""
import bpy
import os

try:
    blend_file = '{blend_file}'
    resolution_percentage = {resolution_percentage}
    new_width = {new_width if new_width is not None else 'None'}
    new_height = {new_height if new_height is not None else 'None'}
    max_frames = {max_frames if max_frames else 'None'}
    maintain_aspect = {maintain_aspect_ratio}
    output_base = r'{output_base}'
    bitrate = {settings['bitrate']}
    preset = '{settings['preset']}'
    crf = '{settings['crf']}'
    
    # Ensure output directory exists
    os.makedirs(output_base, exist_ok=True)
    
    # Get current scene
    scene = bpy.context.scene
    
    # Get original resolution
    original_width = scene.render.resolution_x
    original_height = scene.render.resolution_y
    frame_start = scene.frame_start
    frame_end = scene.frame_end
    frame_duration = frame_end - frame_start + 1
    
    print(f"Processing .blend file: {{blend_file}}")
    print(f"Scene: {{scene.name}}")
    print(f"Original resolution: {{original_width}}x{{original_height}}")
    print(f"Total frames: {{frame_duration}} ({{frame_start}}-{{frame_end}})")
    
    # Calculate target dimensions
    if new_width is None or new_height is None:
        # Use percentage scaling
        scale_factor = resolution_percentage / 100.0
        target_width = int(original_width * scale_factor)
        target_height = int(original_height * scale_factor)
        print(f"Using percentage scaling: {{resolution_percentage}}%")
        print(f"Scale factor: {{scale_factor:.2f}}")
    else:
        # Use specific dimensions
        if maintain_aspect:
            original_aspect = original_width / original_height
            # Use width as reference for aspect ratio
            target_width = new_width
            target_height = int(new_width / original_aspect)
            print(f"Maintaining aspect ratio: {{original_aspect:.2f}}")
        else:
            target_width = new_width
            target_height = new_height
            print(f"Using specific dimensions (aspect ratio may change)")
    
    print(f"Target resolution: {{target_width}}x{{target_height}}")
    
    # Calculate actual percentage for reference
    actual_percentage_w = (target_width / original_width) * 100
    actual_percentage_h = (target_height / original_height) * 100
    print(f"Actual scaling: {{actual_percentage_w:.1f}}% width, {{actual_percentage_h:.1f}}% height")
    
    # Adjust bitrate based on resolution change
    if new_width is None or new_height is None:
        # Adjust bitrate proportionally to resolution change
        bitrate_factor = (resolution_percentage / 100.0) ** 1.5
        adjusted_bitrate = int(bitrate * bitrate_factor)
        adjusted_bitrate = max(1000, min(adjusted_bitrate, 20000))  # Clamp between 1-20 Mbps
        print(f"Adjusted bitrate: {{adjusted_bitrate}} kbps (from {{bitrate}} kbps)")
        bitrate = adjusted_bitrate
    
    # Determine frame range
    if max_frames and max_frames < frame_duration:
        render_end = frame_start + max_frames - 1
        is_preview = True
        print(f"Creating preview: rendering {{max_frames}} of {{frame_duration}} frames")
    else:
        render_end = frame_end
        is_preview = False
        print(f"Rendering full video: {{frame_duration}} frames")
    
    # Set render settings
    scene.frame_start = frame_start
    scene.frame_end = render_end
    scene.render.resolution_x = target_width
    scene.render.resolution_y = target_height
    scene.render.resolution_percentage = 100
    
    # Set up FFMPEG video output
    scene.render.image_settings.file_format = 'FFMPEG'
    scene.render.ffmpeg.format = 'MPEG4'
    scene.render.ffmpeg.codec = 'H264'
    scene.render.ffmpeg.constant_rate_factor = crf
    scene.render.ffmpeg.ffmpeg_preset = preset
    scene.render.ffmpeg.video_bitrate = bitrate
    
    # Audio settings
    scene.render.ffmpeg.audio_codec = 'AAC'
    scene.render.ffmpeg.audio_bitrate = 192
    
    # Save the .blend file with updated settings
    blend_output_path = os.path.join(output_base, "{output_filename}.blend")
    bpy.ops.wm.save_as_mainfile(filepath=blend_output_path)
    print(f"Saved updated .blend file: {{blend_output_path}}")
    
    # Check results
    blend_exists = os.path.exists(blend_output_path)
    
    if blend_exists:
        blend_size = os.path.getsize(blend_output_path)
        blend_size_mb = blend_size / 1024 / 1024
        
        print(f"SUCCESS: Video resolution changed and .blend file saved!")
        print(f"Original: {{original_width}}x{{original_height}} -> New: {{target_width}}x{{target_height}}")
        if new_width is None or new_height is None:
            print(f"Scaling: {{resolution_percentage}}%")
        print(f"Blend file: {{blend_output_path}} ({{blend_size_mb:.2f}} MB)")
        
        if is_preview:
            print(f"NOTE: This is a preview setup. Full video has {{frame_duration}} frames.")
    else:
        raise Exception(f"Blend file not found at {{blend_output_path}}")
        
except Exception as e:
    print(f"ERROR: {{str(e)}}")
    import traceback
    traceback.print_exc()
    raise Exception(str(e))
"""
    
    try:
        blender = BlenderConnection(blender_host, blender_port)
        
        # Calculate timeout based on expected frames
        if max_frames:
            timeout = min(30 + (max_frames * 0.5), 300)  # 0.5 sec per frame, max 5 minutes
        else:
            timeout = 180  # 3 minutes default for full videos
        
        logger.info(f"Using timeout of {timeout} seconds for video rendering")
        
        response = blender.send_command("execute_code", {"code": blender_code}, timeout=timeout)
        return extract_blender_output(response)
            
    except Exception as e:
        return f"❌ Error changing video resolution: {str(e)}"


@mcp.tool()
def apply_negative_effect_to_video(ctx: Context, blend_file: str,
                                   output_filename: Optional[str] = None,
                                   max_frames: Optional[int] = 1,
                                   blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """
    Apply a negative (color inversion) effect to video output from a .blend file.
    Due to processing time, defaults to rendering only 1 frame as preview.
    Saves both the rendered video/image and a new .blend file with compositor nodes.
    
    Args:
        blend_file: Path to .blend file or name if already loaded
        output_filename: Optional output filename (without extension)
        max_frames: Number of frames to process (1 for preview, more for actual video)
        blender_host: Blender server host
        blender_port: Blender server port
    
    Returns:
        Status message with output file location
    """
    
    # Validate file extension
    if not blend_file.lower().endswith('.blend'):
        return f"❌ Error: Input must be a .blend file. Provided: {blend_file}"
    
    logger.info(f"Applying negative effect to video from {blend_file} (max_frames: {max_frames})")
    
    if not output_filename:
        clean_name = os.path.basename(blend_file).replace(".blend", "").replace(" ", "_")
        output_filename = f"{clean_name}_negative"
    
    # Use platform-appropriate output directory
    output_base = OUTPUT_DIR.replace("\\", "/")
    
    blender_code = f"""
import bpy
import os

try:
    blend_file = '{blend_file}'
    output_base = r'{output_base}'
    max_frames = {max_frames}
    
    # Ensure output directory exists
    os.makedirs(output_base, exist_ok=True)
    
    # Get current scene
    scene = bpy.context.scene
    
    print(f"Processing .blend file: {{blend_file}}")
    print(f"Scene: {{scene.name}}")
    
    # Get video properties
    original_width = scene.render.resolution_x
    original_height = scene.render.resolution_y
    frame_start = scene.frame_start
    frame_end = scene.frame_end
    frame_duration = frame_end - frame_start + 1
    
    print(f"Video properties: {{original_width}}x{{original_height}}, {{frame_duration}} frames")
    
    # Enable compositor
    scene.use_nodes = True
    tree = scene.node_tree
    
    # Clear existing nodes
    for node in tree.nodes:
        tree.nodes.remove(node)
    
    # Create render layers node (gets the rendered scene)
    render_layers = tree.nodes.new('CompositorNodeRLayers')
    render_layers.location = (0, 0)
    
    # Invert node
    invert_node = tree.nodes.new('CompositorNodeInvert')
    invert_node.invert_rgb = True
    invert_node.invert_alpha = False
    invert_node.location = (200, 0)
    
    # Composite output node
    composite = tree.nodes.new('CompositorNodeComposite')
    composite.location = (400, 0)
    
    # Connect nodes
    tree.links.new(render_layers.outputs['Image'], invert_node.inputs['Color'])
    tree.links.new(invert_node.outputs['Color'], composite.inputs['Image'])
    
    print("Compositor configured for negative effect")
    
    if max_frames == 1:
        # Single frame preview
        preview_frame = frame_start + (frame_duration // 2)
        scene.frame_set(preview_frame)
        
        # Save the .blend file with compositor setup
        blend_output_path = os.path.join(output_base, "{output_filename}.blend")
        bpy.ops.wm.save_as_mainfile(filepath=blend_output_path)
        print(f"Saved .blend file with negative effect setup: {{blend_output_path}}")
        
        # Check results
        blend_exists = os.path.exists(blend_output_path)
        
        if blend_exists:
            blend_size = os.path.getsize(blend_output_path)
            blend_size_mb = blend_size / 1024 / 1024
            
            print(f"SUCCESS: Negative effect applied to .blend file!")
            print(f"Preview frame {{preview_frame}} configured")
            print(f"Blend file saved to: {{blend_output_path}} ({{blend_size_mb:.2f}} MB)")
            print(f"NOTE: This is a single frame setup. Use max_frames > 1 for video configuration.")
        else:
            raise Exception(f".blend file not found at {{blend_output_path}}")
    
    else:
        # Multiple frames - render as video
        render_end = min(frame_start + max_frames - 1, frame_end)
        frames_to_render = render_end - frame_start + 1
        
        scene.frame_start = frame_start
        scene.frame_end = render_end
        
        # Video output settings
        scene.render.image_settings.file_format = 'FFMPEG'
        scene.render.ffmpeg.format = 'MPEG4'
        scene.render.ffmpeg.codec = 'H264'
        scene.render.ffmpeg.constant_rate_factor = 'MEDIUM'
        scene.render.ffmpeg.ffmpeg_preset = 'GOOD'
        scene.render.ffmpeg.video_bitrate = 6000
        
        # Save the .blend file with compositor setup
        blend_output_path = os.path.join(output_base, "{output_filename}.blend")
        bpy.ops.wm.save_as_mainfile(filepath=blend_output_path)
        print(f"Saved .blend file with negative effect setup: {{blend_output_path}}")
        
        # Check results
        blend_exists = os.path.exists(blend_output_path)
        
        if blend_exists:
            blend_size = os.path.getsize(blend_output_path)
            blend_size_mb = blend_size / 1024 / 1024
            
            print(f"SUCCESS: Negative effect applied to .blend file!")
            print(f"Frames configured: {{frames_to_render}} of {{frame_duration}}")
            print(f"Blend file: {{blend_output_path}} ({{blend_size_mb:.2f}} MB)")
            
            if is_partial:
                print(f"NOTE: Partial configuration. Full video has {{frame_duration}} frames.")
        else:
            raise Exception(f".blend file not found at {{blend_output_path}}")
        
except Exception as e:
    print(f"ERROR: {{str(e)}}")
    import traceback
    traceback.print_exc()
    raise Exception(str(e))
"""
    
    try:
        blender = BlenderConnection(blender_host, blender_port)
        
        # Calculate timeout
        timeout = 30 if max_frames == 1 else min(30 + (max_frames * 0.5), 180)
        
        response = blender.send_command("execute_code", {"code": blender_code}, timeout=timeout)
        return extract_blender_output(response)
            
    except Exception as e:
        return f"❌ Error applying negative effect to video: {str(e)}"
    

@mcp.tool()
def upload_to_cos_output(ctx: Context, local_file_path: str, 
                        object_key: Optional[str] = None) -> str:
    """Upload a processed file from Blender to the COS output bucket (uploads to volumetric-output)"""
    try:
        if not COS_API_BASE_URL:
            return "❌ COS_API_BASE_URL not configured. Please set the CODE_ENGINE_URL environment variable."
        
        # Handle relative paths
        if not os.path.isabs(local_file_path):
            local_file_path = os.path.join(OUTPUT_DIR, local_file_path)
        
        if not os.path.exists(local_file_path):
            return f"❌ File not found: {local_file_path}"
        
        # Use filename as object key if not provided
        if not object_key:
            object_key = os.path.basename(local_file_path)
        
        # Read file
        with open(local_file_path, 'rb') as f:
            file_content = f.read()
        
        file_size = len(file_content)
        logger.info(f"Uploading {object_key} ({file_size} bytes) to volumetric-output bucket")
        
        # Upload to COS - endpoint is /buckets/upload
        # Note: cos_app.py uploads to BUCKET_NAME_2 (volumetric-output)
        upload_url = f"{COS_API_BASE_URL}/buckets/upload"
        
        files = {'file': (object_key, file_content)}
        response = requests.post(upload_url, files=files, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        if "presigned_url" in result:
            return f"✅ File uploaded successfully to bucket 'volumetric-output' as '{object_key}'\nSize: {file_size} bytes\nPresigned URL: {result['presigned_url']}"
        else:
            message = result.get("message", "File uploaded")
            return f"✅ {message}\nUploaded to 'volumetric-output' as '{object_key}'\nSize: {file_size} bytes"
            
    except requests.exceptions.RequestException as e:
        return f"❌ Failed to upload to COS: {str(e)}"
    except Exception as e:
        return f"❌ Error uploading file: {str(e)}"


@mcp.tool()
def execute_custom_blender_code(ctx: Context, code: str, blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """Execute custom Python code in Blender for advanced operations"""
    try:
        logger.info("Executing custom Blender code")
        blender = BlenderConnection(blender_host, blender_port)
        
        # Add safety wrapper with platform info
        wrapped_code = f"""
import bpy
import sys
import traceback
import platform

# Platform info
print(f"Platform: {{platform.platform()}}")
print(f"Blender: {{bpy.app.version_string}}")

try:
    # User code starts here
{code}
    # User code ends here
    
    if not any(line.strip().startswith('print') for line in '''{code}'''.split('\\n')):
        print("SUCCESS: Custom code executed successfully")
except Exception as e:
    error_msg = f"ERROR in custom code: {{str(e)}}"
    print(error_msg)
    traceback.print_exc()
"""
        
        response = blender.send_command("execute_code", {"code": wrapped_code}, timeout=60)
        return extract_blender_output(response)
        
    except Exception as e:
        return f"❌ Error executing custom code: {str(e)}"


@mcp.tool()
def batch_process_blend_files(ctx: Context, 
                              operation: str = "list", 
                              operation_params: Optional[str] = None,
                              blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """
    Batch process multiple .blend files from COS (volumetric-input bucket).
    Operations: 'list', 'resize', 'negative'
    operation_params: JSON string, e.g., '{"percentage": 50}' for resize
    """
    try:
        if not COS_API_BASE_URL:
            return "❌ COS_API_BASE_URL not configured. Please set the CODE_ENGINE_URL environment variable."
        
        # Parse operation params if provided
        params = {}
        if operation_params:
            try:
                import json
                params = json.loads(operation_params)
            except:
                logger.warning(f"Failed to parse operation_params: {operation_params}")
        
        # List all objects in volumetric-input bucket
        url = f"{COS_API_BASE_URL}/buckets/objects"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        objects = response.json()
        if not objects:
            return f"📦 No objects found in volumetric-input bucket"
        
        # Filter for .blend files only
        blend_files = [obj for obj in objects if obj.lower().endswith('.blend')]
        
        if not blend_files:
            return f"📦 No .blend files found in volumetric-input bucket"
        
        if operation == "list":
            result = f"📦 Found {len(blend_files)} .blend files in volumetric-input bucket:\n"
            for obj in blend_files:
                result += f"  - {obj}\n"
            return result
        
        results = []
        results.append(f"🔄 Processing {min(len(blend_files), 3)} of {len(blend_files)} .blend files:")
        
        blender = BlenderConnection(blender_host, blender_port)
        processed_count = 0
        failed_count = 0
        
        for blend_file in blend_files[:3]:  # Limit to 3 files to avoid timeout
            try:
                results.append(f"\n📥 Processing: {blend_file}")
                
                # Load the blend file
                load_result = load_blend_file_from_cos(ctx, blend_file, blender_host, blender_port)
                if "ERROR" in load_result or "❌" in load_result:
                    failed_count += 1
                    results.append(f"  ❌ Failed to load")
                    continue
                
                # Apply operation based on type
                if operation == "resize":
                    percentage = params.get("percentage", 50)
                    results.append(f"  ↔️ Resizing to {percentage}%...")
                    
                    resize_result = change_video_resolution(
                        ctx, blend_file,
                        resolution_percentage=percentage,
                        output_filename=f"{os.path.basename(blend_file).replace('.blend', '')}_resized",
                        max_frames=10, quality="medium",
                        maintain_aspect_ratio=True,
                        blender_host=blender_host, blender_port=blender_port
                    )
                    
                    if "SUCCESS" in resize_result or "✅" in resize_result:
                        processed_count += 1
                        results.append(f"  ✅ Resized successfully")
                    else:
                        failed_count += 1
                        results.append(f"  ❌ Resize failed")
                    
                elif operation == "negative":
                    results.append(f"  🎨 Applying negative effect...")
                    
                    negative_result = apply_negative_effect_to_video(
                        ctx, blend_file,
                        output_filename=f"{os.path.basename(blend_file).replace('.blend', '')}_negative",
                        max_frames=1,
                        blender_host=blender_host, blender_port=blender_port
                    )
                    
                    if "SUCCESS" in negative_result or "✅" in negative_result:
                        processed_count += 1
                        results.append(f"  ✅ Negative effect applied")
                    else:
                        failed_count += 1
                        results.append(f"  ❌ Negative effect failed")
                
            except Exception as e:
                failed_count += 1
                results.append(f"  ❌ Failed: {str(e)}")
                continue
        
        results.append(f"\n📊 Summary: ✅ {processed_count} processed, ❌ {failed_count} failed")
        return "\n".join(results)
        
    except Exception as e:
        return f"❌ Batch processing failed: {str(e)}"


@mcp.tool()
def get_system_info(ctx: Context, blender_host: str = "localhost", blender_port: int = 9876) -> str:
    """Get system and Blender configuration information"""
    try:
        # Get local system info
        import platform
        local_info = f"""
📊 MCP Server System Info:
- Platform: {platform.platform()}
- Python: {platform.python_version()}
- Work Directory: {WORK_DIR}
- Output Directory: {OUTPUT_DIR}
- COS URL: {COS_API_BASE_URL or 'Not configured'}
"""
        
        # Get Blender system info
        blender = BlenderConnection(blender_host, blender_port)
        blender_code = """
import bpy
import sys
import platform
import os

print("📊 Blender System Info:")
print(f"- Blender Version: {bpy.app.version_string}")
print(f"- Python Version: {sys.version}")
print(f"- Platform: {platform.platform()}")
print(f"- Temp Directory: {bpy.app.tempdir}")
print(f"- User Scripts: {bpy.utils.user_resource('SCRIPTS')}")

# Check for FFMPEG
print(f"- FFMPEG Available: {bpy.app.ffmpeg.supported}")
if bpy.app.ffmpeg.supported:
    print(f"  - Audio Channels: {bpy.app.ffmpeg.supported_audio_channels}")
    print(f"  - Audio Codecs: {bpy.app.ffmpeg.supported_audio_codecs[:3]}...")  # First 3
    print(f"  - Video Codecs: {bpy.app.ffmpeg.supported_video_codecs[:3]}...")  # First 3

# Memory info
print(f"- Memory Peak: {bpy.app.memory_usage_peak / 1024 / 1024:.2f} MB")

# Current scene info
scene = bpy.context.scene
print(f"\\n📊 Current Scene:")
print(f"- Name: {scene.name}")
print(f"- Resolution: {scene.render.resolution_x}x{scene.render.resolution_y}")
print(f"- FPS: {scene.render.fps}")
print(f"- Frame Range: {scene.frame_start}-{scene.frame_end}")
"""
        
        response = blender.send_command("execute_code", {"code": blender_code})
        blender_info = extract_blender_output(response)
        
        return f"{local_info}\n{blender_info}"
        
    except Exception as e:
        return f"{local_info}\n❌ Could not get Blender info: {str(e)}"


@mcp.tool()
def cleanup_workspace(ctx: Context) -> str:
    """Clean up temporary files in the workspace directory"""
    try:
        cleaned_files = []
        cleaned_size = 0
        
        # Clean files in WORK_DIR
        if os.path.exists(WORK_DIR):
            for file in os.listdir(WORK_DIR):
                if file.startswith('.') or file == '.write_test':
                    continue
                    
                file_path = os.path.join(WORK_DIR, file)
                if os.path.isfile(file_path):
                    try:
                        size = os.path.getsize(file_path)
                        os.remove(file_path)
                        cleaned_files.append(file)
                        cleaned_size += size
                    except Exception as e:
                        logger.warning(f"Could not remove {file}: {e}")
        
        if cleaned_files:
            return f"✅ Cleaned {len(cleaned_files)} files ({cleaned_size / 1024 / 1024:.2f} MB)\nFiles removed: {', '.join(cleaned_files[:5])}{'...' if len(cleaned_files) > 5 else ''}"
        else:
            return "✅ Workspace is already clean"
            
    except Exception as e:
        return f"❌ Error cleaning workspace: {str(e)}"


# Main execution
def main():
    """Run the MCP server"""
    import sys
    import signal
    
    # Check for command line arguments
    port = int(os.getenv("MCP_PORT", 8080))
    host = os.getenv("MCP_HOST", "0.0.0.0")
    
    for i, arg in enumerate(sys.argv):
        if arg == "--port" and i + 1 < len(sys.argv):
            port = int(sys.argv[i + 1])
        elif arg == "--host" and i + 1 < len(sys.argv):
            host = sys.argv[i + 1]
    
    logger.info(f"Starting Blender MCP Server on {host}:{port}")
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"Work Directory: {WORK_DIR}")
    logger.info(f"Output Directory: {OUTPUT_DIR}")
    logger.info(f"Server Focus: Processing .blend files for video operations")
    
    # Handle graceful shutdown
    def signal_handler(sig, frame):
        logger.info("Shutting down MCP server...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    mcp.settings.port = port
    mcp.settings.host = host
    
    # Run with proper transport
    mcp.run(
        transport="streamable-http"
    )
    
if __name__ == "__main__":
    main()