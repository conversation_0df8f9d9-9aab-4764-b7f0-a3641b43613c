import json
import sys
import requests
from typing import Any, Dict, Optional
import base64
import argparse
import re
from mcp.server.fastmcp import FastMCP


mcp = FastMCP("blender-proxy-server")


WEBHOOK_URL = "https://csm.int-aws-de.webmethods.io/runflow/run/sync/v2/3aqJ5EB3F"
USERNAME = "techx_user_15"
PASSWORD = "CSMUser@123456789"
MCP_ENDPOINT = "http://52.118.148.178:8080/mcp"


def get_auth_header() -> Dict[str, str]:
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return {"Authorization": f"Basic {encoded}"}

def parse_sse_response(response_text: str) -> Dict:
    """Parse Server-Sent Events (SSE) format response"""
    if response_text.startswith("event:"):
        match = re.search(r'data:\s*({.*})', response_text, re.DOTALL)
        if match:
            try:
                json_data = json.loads(match.group(1))
                if "result" in json_data:
                    result = json_data["result"]
                    if "structuredContent" in result:
                        return result["structuredContent"]
                    elif "content" in result:
                        # Extract text from content array
                        if isinstance(result["content"], list) and len(result["content"]) > 0:
                            return {"result": result["content"][0].get("text", "")}
                return json_data
            except json.JSONDecodeError:
                pass
    
    try:
        return json.loads(response_text)
    except json.JSONDecodeError:
        return {"result": response_text}

def call_webhook(tool_name: str, arguments: Optional[Dict] = None) -> Dict:
    """Call the WebMethods webhook with tool-specific payload"""
    try:
        headers = get_auth_header()
        headers["Content-Type"] = "application/json"
        if tool_name in ["test_blender_connection", "list_cos_bucket_objects", "get_system_info"]:
            body = {
                "MCP_ENDPOINT": MCP_ENDPOINT,
                "TOOL_NAME": tool_name
            }
        else:
            body = {
                "MCP_ENDPOINT": MCP_ENDPOINT,
                "TOOL_NAME": tool_name,
                "ARGUMENTS": json.dumps(arguments) if arguments else "{}"
            }
        
        response = requests.post(
            WEBHOOK_URL,
            json=body,
            headers=headers,
            timeout=60
        )
        
        response_text = response.text
        
        if response.status_code == 200:
            parsed_response = parse_sse_response(response_text)
            return {"success": True, "data": parsed_response}
        else:
            try:
                error_data = json.loads(response_text)
                if "error" in error_data:
                    return {"success": False, "error": error_data["error"]}
            except:
                pass
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response_text}"
            }
            
    except requests.exceptions.Timeout:
        return {"success": False, "error": "Request timeout"}
    except requests.exceptions.RequestException as e:
        return {"success": False, "error": f"Request failed: {str(e)}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
def test_blender_connection() -> Dict:
    """
    Test connection to the Blender MCP server.
    Returns connection status and server information.
    """
    result = call_webhook("test_blender_connection")
    
    if result["success"]:
        return result["data"]
    else:
        return {"error": result["error"]}

@mcp.tool()
def list_cos_bucket_objects() -> Dict:
    """
    List all objects available in the COS bucket.
    Returns a list of media files that can be loaded into Blender.
    """
    result = call_webhook("list_cos_bucket_objects")
    
    if result["success"]:
        return result["data"]
    else:
        return {"error": result["error"]}

@mcp.tool()
def load_blend_file_from_cos(object_key: str) -> Dict:
    """
    Load a blend file from COS bucket into Blender workspace.
    
    Args:
        object_key: The key/name of the object in COS bucket (e.g., "input.blend")
    
    Returns:
        Status of the media loading operation
    """
    arguments = {"object_key": object_key}
    result = call_webhook("load_blend_file_from_cos", arguments)
    
    if result["success"]:
        return result["data"]
    else:
        return {"error": result["error"]}


@mcp.tool()
def change_video_resolution(blend_file: str, resolution_percentage: int = 100) -> Dict:
    """
    Change the resolution of video output from a .blend file.
    Saves a new .blend file with updated settings.
    
    Args:
        blend_file: Path to .blend file or name if already loaded
        resolution_percentage: Percentage to scale (e.g., 50 for half size, 200 for double)
    
    Returns:
        Status message with output .blend file location
    """
    arguments = {
        "blend_file": blend_file,
        "resolution_percentage": resolution_percentage
    }
    result = call_webhook("change_video_resolution", arguments)
    
    if result["success"]:
        return result["data"]
    else:
        return {"error": result["error"]}

@mcp.tool()
def get_system_info() -> Dict:
    """
    Get system and Blender configuration information.
    Returns detailed information about both the MCP server and Blender environment.
    """
    result = call_webhook("get_system_info")
    
    if result["success"]:
        return result["data"]
    else:
        return {"error": result["error"]}

def main():
    """Run the MCP server with command-line interface"""
    parser = argparse.ArgumentParser(description="Blender MCP Server with WebMethods Integration")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    test_parser = subparsers.add_parser('test-connection', help='Test Blender MCP connection')
    list_cos_parser = subparsers.add_parser('list-cos', help='List objects in COS bucket')
    
    load_parser = subparsers.add_parser('load-blend', help='Load .blend file from COS bucket')
    load_parser.add_argument('--key', required=True, help='Object key in COS bucket (e.g., input.blend)')
    

    list_media_parser = subparsers.add_parser('list-media', help='List media files in Blender workspace')
    
    resolution_parser = subparsers.add_parser('change-resolution', help='Change video resolution in blend file')
    resolution_parser.add_argument('--blend-file', required=True, help='Blend file name or path')
    resolution_parser.add_argument('--percentage', type=int, default=100, help='Resolution percentage (default: 100)')
    

    system_parser = subparsers.add_parser('system-info', help='Get system and Blender configuration')
    
    
    args = parser.parse_args()
    
    if args.command == 'test-connection':
        result = test_blender_connection()
        print(json.dumps(result, indent=2))
    elif args.command == 'list-cos':
        result = list_cos_bucket_objects()
        print(json.dumps(result, indent=2))
    elif args.command == 'load-blend':
        result = load_blend_file_from_cos(args.key)
        print(json.dumps(result, indent=2))
    elif args.command == 'change-resolution':
        result = change_video_resolution(args.blend_file, args.percentage)
        print(json.dumps(result, indent=2))
    elif args.command == 'system-info':
        result = get_system_info()
        print(json.dumps(result, indent=2))
    else:
        print(f"Starting Blender MCP Server...")
        print(f"Webhook URL: {WEBHOOK_URL}")
        print(f"MCP Endpoint: {MCP_ENDPOINT}")
        mcp.run()

if __name__ == "__main__":
    main()